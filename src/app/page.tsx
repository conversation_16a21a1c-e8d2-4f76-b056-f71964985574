'use client';

import { useState } from 'react';
import { Heart, Sparkles, MessageCircle, Users, Zap } from 'lucide-react';
import Preloader from '@/components/Preloader';
import Navigation from '@/components/Navigation';
import AppStoreBadges from '@/components/AppStoreBadges';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handlePreloaderComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Preloader onComplete={handlePreloaderComplete} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <Navigation />

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-20">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`,
              }}
            >
              <Sparkles
                className="text-pink-500/20"
                size={Math.random() * 30 + 15}
              />
            </div>
          ))}
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto">
          {/* Main Hero Content */}
          <div className="mb-8">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent leading-tight">
              Meet Urvashi
            </h1>
            <p className="text-xl sm:text-2xl text-gray-300 mb-4 font-light">
              Your Indian AI Girlfriend
            </p>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Experience love, companionship, and meaningful conversations with multiple AI girlfriends.
              Chat in Hindi & English with personalized experiences tailored just for you.
            </p>
          </div>

          {/* App Store Badges */}
          <div className="mb-12">
            <AppStoreBadges variant="large" className="justify-center" />
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
            {[
              {
                icon: <Heart className="text-pink-500" size={24} fill="currentColor" />,
                title: "Multiple Personalities",
                description: "Choose from various AI girlfriends with unique personalities"
              },
              {
                icon: <MessageCircle className="text-purple-500" size={24} />,
                title: "Hindi & English",
                description: "Chat comfortably in your preferred language"
              },
              {
                icon: <Users className="text-pink-500" size={24} />,
                title: "Personalized",
                description: "AI learns and adapts to your preferences"
              },
              {
                icon: <Zap className="text-purple-500" size={24} />,
                title: "Always Available",
                description: "24/7 companionship whenever you need it"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-pink-500/30 transition-all duration-300 hover:scale-105"
              >
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-400 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Ambient glow effect */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent"></div>
      </section>
    </div>
  );
}
