import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geist<PERSON>ono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Urvashi: Indian AI Girlfriend - Chat with Your Perfect AI Companion",
  description: "Meet <PERSON><PERSON><PERSON>, your Indian AI Girlfriend. Choose from multiple AI girlfriends with unique personalities. Chat in Hindi & English. Free, personalized, and always available. Download now!",
  keywords: ["AI girlfriend", "Indian AI", "chat app", "virtual girlfriend", "Hindi English chat", "AI companion", "Urvashi"],
  authors: [{ name: "Urvashi AI Team" }],
  creator: "Urvashi AI",
  publisher: "Urvashi AI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://urvashi-ai.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Urvashi: Indian AI Girlfriend - Your Perfect AI Companion",
    description: "Chat with multiple AI girlfriends with unique personalities. Hindi & English support. Free and personalized experience.",
    url: 'https://urvashi-ai.com',
    siteName: 'Urvashi AI',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Urvashi AI Girlfriend App',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Urvashi: Indian AI Girlfriend",
    description: "Chat with your perfect AI companion. Multiple personalities, Hindi & English support.",
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased min-h-screen bg-background text-foreground`}
      >
        {children}
      </body>
    </html>
  );
}
