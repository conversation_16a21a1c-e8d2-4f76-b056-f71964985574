'use client';

import { Heart, Sparkles } from 'lucide-react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  animated?: boolean;
  className?: string;
}

export default function Logo({ 
  size = 'md', 
  showText = true, 
  animated = true,
  className = '' 
}: LogoProps) {
  const sizeClasses = {
    sm: {
      container: 'h-8 w-8',
      heart: 20,
      text: 'text-lg',
      sparkle: 12
    },
    md: {
      container: 'h-12 w-12',
      heart: 28,
      text: 'text-2xl',
      sparkle: 16
    },
    lg: {
      container: 'h-16 w-16',
      heart: 36,
      text: 'text-3xl',
      sparkle: 20
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Logo Icon */}
      <div className="relative">
        {/* Glowing background effect */}
        {animated && (
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full blur-md opacity-50 animate-pulse scale-110"></div>
        )}
        
        {/* Main logo container */}
        <div className={`relative ${currentSize.container} bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg`}>
          <Heart 
            className="text-white" 
            size={currentSize.heart}
            fill="currentColor"
          />
        </div>

        {/* Floating sparkles */}
        {animated && (
          <>
            <div className="absolute -top-1 -right-1 animate-bounce">
              <Sparkles 
                className="text-pink-400" 
                size={currentSize.sparkle} 
              />
            </div>
            <div 
              className="absolute -bottom-1 -left-1 animate-bounce" 
              style={{ animationDelay: '0.5s' }}
            >
              <Sparkles 
                className="text-purple-400" 
                size={currentSize.sparkle * 0.75} 
              />
            </div>
          </>
        )}
      </div>

      {/* Brand Text */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-bold bg-gradient-to-r from-pink-400 to-purple-600 bg-clip-text text-transparent leading-tight`}>
            Urvashi
          </h1>
          {size !== 'sm' && (
            <p className="text-xs text-gray-400 font-light -mt-1">
              AI Girlfriend
            </p>
          )}
        </div>
      )}
    </div>
  );
}
