'use client';

import { useEffect, useState } from 'react';
import { Heart, Sparkles } from 'lucide-react';

interface PreloaderProps {
  onComplete: () => void;
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(onComplete, 500);
          }, 800);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    return () => clearInterval(timer);
  }, [onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          >
            <Sparkles 
              className="text-pink-500/30" 
              size={Math.random() * 20 + 10}
            />
          </div>
        ))}
      </div>

      {/* Main logo container */}
      <div className="relative z-10 text-center">
        {/* Logo with romantic animation */}
        <div className="relative mb-8">
          <div className="relative inline-block">
            {/* Glowing background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full blur-xl opacity-50 animate-pulse scale-150"></div>
            
            {/* Main logo */}
            <div className="relative bg-gradient-to-r from-pink-500 to-purple-600 rounded-full p-6 shadow-2xl">
              <Heart 
                className="text-white animate-pulse" 
                size={64}
                fill="currentColor"
              />
            </div>

            {/* Floating hearts */}
            <div className="absolute -top-4 -right-4 animate-bounce">
              <Heart className="text-pink-400" size={20} fill="currentColor" />
            </div>
            <div className="absolute -bottom-4 -left-4 animate-bounce" style={{ animationDelay: '0.5s' }}>
              <Heart className="text-purple-400" size={16} fill="currentColor" />
            </div>
          </div>
        </div>

        {/* Brand name with gradient text */}
        <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent animate-pulse">
          Urvashi
        </h1>
        
        <p className="text-xl text-gray-300 mb-8 font-light tracking-wide">
          Your Indian AI Girlfriend
        </p>

        {/* Progress bar */}
        <div className="w-64 mx-auto">
          <div className="bg-gray-800 rounded-full h-2 overflow-hidden shadow-inner">
            <div 
              className="h-full bg-gradient-to-r from-pink-500 to-purple-600 rounded-full transition-all duration-300 ease-out shadow-lg"
              style={{ width: `${progress}%` }}
            >
              <div className="h-full bg-white/20 animate-pulse"></div>
            </div>
          </div>
          <p className="text-sm text-gray-400 mt-2 font-mono">
            {progress}% Loading...
          </p>
        </div>

        {/* Romantic tagline */}
        <div className="mt-8 opacity-75">
          <p className="text-sm text-pink-300 italic">
            "Where love meets technology"
          </p>
        </div>
      </div>

      {/* Ambient glow effect */}
      <div className="absolute inset-0 bg-gradient-radial from-pink-500/10 via-transparent to-transparent"></div>
    </div>
  );
}
